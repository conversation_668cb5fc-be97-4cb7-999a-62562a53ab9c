import { useEffect, useState, useCallback } from 'react';
import { extensionBridge, ExtensionRecordingData } from '../../../services/extension-bridge';
import { useExternalRecordingActions } from '../store/use-screen-recording-store';
import { RecordingPackage } from '../store/use-screen-recording-store';

export interface ExtensionBridgeState {
  isConnected: boolean;
  isPolling: boolean;
  stats: {
    total: number;
    processed: number;
    unprocessed: number;
  };
  lastRecording: ExtensionRecordingData | null;
  error: string | null;
}

export interface ExtensionBridgeControls {
  checkForRecordings: () => Promise<void>;
  importRecording: (data: ExtensionRecordingData) => Promise<void>;
  cleanupOldRecordings: () => void;
  refreshStats: () => void;
}

export function useExtensionBridge(): ExtensionBridgeState & ExtensionBridgeControls {
  const [state, setState] = useState<ExtensionBridgeState>({
    isConnected: true,
    isPolling: true,
    stats: { total: 0, processed: 0, unprocessed: 0 },
    lastRecording: null,
    error: null,
  });

  const { addExternalRecording, addToTimeline } = useExternalRecordingActions();

  // Handle new recordings from the extension
  const handleNewRecording = useCallback(async (data: ExtensionRecordingData) => {
    try {
      console.log('🎬 Processing new recording from extension:', data);
      
      // Convert the extension data to our recording package format
      const videoBlob = extensionBridge.base64ToBlob(data.video.blobData, data.video.mimeType);
      
      const recordingPackage: RecordingPackage = {
        video: {
          blob: videoBlob,
          filename: data.video.filename,
          duration: data.video.duration,
          dimensions: data.video.dimensions,
          mimeType: data.video.mimeType,
        },
        mouseTracking: data.mouseTracking,
        recordingMetadata: data.recordingMetadata,
      };

      // Add to external recording store
      const recording = await addExternalRecording(recordingPackage);
      
      // Automatically add to timeline
      await addToTimeline(recording);

      setState(prev => ({
        ...prev,
        lastRecording: data,
        error: null,
      }));

      console.log('✅ Recording imported and added to timeline successfully');

    } catch (error) {
      console.error('❌ Failed to import recording from extension:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to import recording',
      }));
    }
  }, [addExternalRecording, addToTimeline]);

  // Set up the extension bridge callback
  useEffect(() => {
    extensionBridge.onNewRecording(handleNewRecording);
    
    // Initial stats refresh
    refreshStats();
    
    // Cleanup old recordings on mount
    extensionBridge.cleanupOldRecordings();

    return () => {
      // Note: We don't stop polling here as it's a singleton service
      // that might be used by other components
    };
  }, [handleNewRecording]);

  const checkForRecordings = useCallback(async () => {
    try {
      await extensionBridge.checkNow();
      refreshStats();
    } catch (error) {
      console.error('Error checking for recordings:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to check for recordings',
      }));
    }
  }, []);

  const importRecording = useCallback(async (data: ExtensionRecordingData) => {
    await handleNewRecording(data);
  }, [handleNewRecording]);

  const cleanupOldRecordings = useCallback(() => {
    try {
      extensionBridge.cleanupOldRecordings();
      refreshStats();
    } catch (error) {
      console.error('Error cleaning up recordings:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to cleanup recordings',
      }));
    }
  }, []);

  const refreshStats = useCallback(() => {
    try {
      const stats = extensionBridge.getStats();
      setState(prev => ({
        ...prev,
        stats,
        error: null,
      }));
    } catch (error) {
      console.error('Error refreshing stats:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to refresh stats',
      }));
    }
  }, []);

  return {
    ...state,
    checkForRecordings,
    importRecording,
    cleanupOldRecordings,
    refreshStats,
  };
}

/**
 * Hook for getting unprocessed recordings without auto-import
 */
export function useUnprocessedRecordings() {
  const [recordings, setRecordings] = useState<ExtensionRecordingData[]>([]);
  
  const refreshRecordings = useCallback(() => {
    const unprocessed = extensionBridge.getAllUnprocessedRecordings();
    setRecordings(unprocessed);
  }, []);

  useEffect(() => {
    refreshRecordings();
    
    // Refresh every 5 seconds
    const interval = setInterval(refreshRecordings, 5000);
    
    return () => clearInterval(interval);
  }, [refreshRecordings]);

  return {
    recordings,
    refreshRecordings,
  };
}
