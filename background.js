var coord_list = [];

// Handle extension icon click - open control page
chrome.action.onClicked.addListener(async () => {
  // Create or focus the control page
  const url = chrome.runtime.getURL('control.html');

  // Check if control page is already open
  const tabs = await chrome.tabs.query({ url: url });

  if (tabs.length > 0) {
    // Focus existing tab
    chrome.tabs.update(tabs[0].id, { active: true });
    chrome.windows.update(tabs[0].windowId, { focused: true });
  } else {
    // Create new tab
    chrome.tabs.create({ url: url });
  }
});

// Handle messages from content scripts and control page
chrome.runtime.onMessage.addListener(async (msg, sender, sendResponse) => {
  console.log('Background received message:', msg, 'from:', sender);

  if (msg.origin === "content") {
    // Mouse tracking data from injected content script
    coord_list.push(msg.content);
    console.log('Added mouse data:', msg.content, 'total points:', coord_list.length);

    // Send acknowledgment back to content script
    sendResponse({ received: true, totalPoints: coord_list.length });
  } else if (msg.origin === "control" && msg.content.action === "stop") {
    // Control page requesting mouse data
    console.log('Sending mouse data to control page, total points:', coord_list.length);

    // Find the control page tab
    const controlUrl = chrome.runtime.getURL('control.html');
    const tabs = await chrome.tabs.query({ url: controlUrl });

    if (tabs.length > 0) {
      // Send message to the control page tab
      chrome.tabs.sendMessage(tabs[0].id, {
        origin: "background",
        content: coord_list,
      });
    }

    coord_list = [];
    sendResponse({ sent: true });
  } else {
    // Other mouse tracking data (fallback)
    console.log('Fallback: adding mouse data:', msg.content);
    coord_list.push(msg.content);
    sendResponse({ received: true, totalPoints: coord_list.length });
  }

  return true; // Keep message channel open for async response
});

// Initialize extension
chrome.runtime.onInstalled.addListener(() => {
  console.log('Mouse Tracker & Screen Recorder installed');

  // Set initial storage values
  chrome.storage.sync.set({
    isRecording: false,
    autoExportToEditor: true,
    autoOpenEditor: true
  });
});
