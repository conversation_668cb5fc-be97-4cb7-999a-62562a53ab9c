class MouseTrackerRecorder {
    constructor() {
        this.isRecording = false;
        this.mediaRecorder = null;
        this.recordedChunks = [];
        this.mouseData = [];
        this.currentTab = null;
        this.sessionStartTime = null;
        
        this.initializeElements();
        this.setupEventListeners();
        this.loadState();
    }

    initializeElements() {
        this.startBtn = document.getElementById('startRecording');
        this.stopBtn = document.getElementById('stopRecording');
        this.downloadBtn = document.getElementById('downloadRecording');
        this.exportToVideoEditorBtn = document.getElementById('exportToVideoEditor');
        this.refreshTargetBtn = document.getElementById('refreshTarget');
        this.testMouseTrackingBtn = document.getElementById('testMouseTracking');
        this.openTestPageBtn = document.getElementById('openTestPage');
        this.statusText = document.getElementById('recording-status');
        this.statusDot = document.getElementById('status-dot');
        this.resultsSection = document.getElementById('resultsSection');
        this.coordCount = document.getElementById('coordCount');
        this.clickCount = document.getElementById('clickCount');
        this.recordedVideo = document.getElementById('recordedVideo');
        this.mouseVisualization = document.getElementById('mouseVisualization');
        this.copyMouseDataBtn = document.getElementById('copyMouseData');
        this.tabInfoSection = document.getElementById('tabInfoSection');
        this.targetTabTitle = document.getElementById('targetTabTitle');
        this.targetTabUrl = document.getElementById('targetTabUrl');
        this.tabOptionTitle = document.getElementById('tabOptionTitle');
        this.debugInfo = document.getElementById('debugInfo');
        this.debugText = document.getElementById('debugText');
        this.autoExportToEditorCheckbox = document.getElementById('autoExportToEditor');
        this.autoOpenEditorCheckbox = document.getElementById('autoOpenEditor');
    }

    setupEventListeners() {
        this.startBtn.addEventListener('click', () => this.startRecording());
        this.stopBtn.addEventListener('click', () => this.stopRecording());
        this.downloadBtn.addEventListener('click', () => this.downloadRecording());
        this.exportToVideoEditorBtn.addEventListener('click', () => this.exportToVideoEditor());
        this.refreshTargetBtn.addEventListener('click', () => this.updateTargetTabInfo());
        this.testMouseTrackingBtn.addEventListener('click', () => this.testMouseTracking());
        this.openTestPageBtn.addEventListener('click', () => this.openTestPage());
        this.copyMouseDataBtn.addEventListener('click', () => this.copyMouseData());

        // Listen for recording type changes
        document.querySelectorAll('input[name="recordingType"]').forEach(radio => {
            radio.addEventListener('change', () => this.updateTargetTabInfo());
        });

        // Listen for mouse tracking checkbox changes
        document.getElementById('enableMouseTracking').addEventListener('change', () => this.updateTargetTabInfo());

        // Listen for messages from background script
        chrome.runtime.onMessage.addListener((msg) => {
            console.log('Control page received message:', msg);
            if (msg.origin === 'background') {
                this.handleMouseData(msg.content);
            }
        });
    }

    async loadState() {
        const result = await chrome.storage.sync.get([
            'isRecording',
            'autoExportToEditor',
            'autoOpenEditor'
        ]);

        if (result.isRecording) {
            this.updateUIState(true);
        }

        // Load auto-export settings
        if (result.autoExportToEditor !== undefined) {
            this.autoExportToEditorCheckbox.checked = result.autoExportToEditor;
        }
        if (result.autoOpenEditor !== undefined) {
            this.autoOpenEditorCheckbox.checked = result.autoOpenEditor;
        }

        // Save settings when checkboxes change
        this.autoExportToEditorCheckbox.addEventListener('change', () => {
            chrome.storage.sync.set({ autoExportToEditor: this.autoExportToEditorCheckbox.checked });
        });
        this.autoOpenEditorCheckbox.addEventListener('change', () => {
            chrome.storage.sync.set({ autoOpenEditor: this.autoOpenEditorCheckbox.checked });
        });

        // Update target tab info on load
        await this.updateTargetTabInfo();
    }

    async getTargetTab() {
        // Get all tabs
        const tabs = await chrome.tabs.query({});

        // Find a suitable tab (not extension pages, not chrome:// pages)
        const suitableTabs = tabs.filter(tab =>
            (tab.url.startsWith('http://') || tab.url.startsWith('https://')) &&
            !tab.url.includes('chrome-extension://')
        );

        if (suitableTabs.length === 0) {
            return null;
        }

        // Don't use the active tab if it's the control page
        const controlUrl = chrome.runtime.getURL('control.html');
        const nonControlTabs = suitableTabs.filter(tab => tab.url !== controlUrl);

        if (nonControlTabs.length === 0) {
            return null;
        }

        // Prefer the most recently used suitable tab
        const sortedTabs = nonControlTabs.sort((a, b) => b.lastAccessed - a.lastAccessed);
        return sortedTabs[0];
    }

    async updateTargetTabInfo() {
        // Always get target tab info to update the option title
        const targetTab = await this.getTargetTab();

        // Update the tab option title
        if (targetTab) {
            this.tabOptionTitle.textContent = targetTab.title || 'Untitled';

            // Update debug info
            this.debugInfo.style.display = 'block';
            this.debugText.textContent = `Target tab ready: ${targetTab.title}`;
        } else {
            this.tabOptionTitle.textContent = 'No suitable tab found';

            // Update debug info
            this.debugInfo.style.display = 'block';
            this.debugText.textContent = 'No suitable tab found - open a web page first';
        }

        // Hide the target tab info section since we're showing the name in the option card
        this.tabInfoSection.style.display = 'none';
    }

    async testMouseTracking() {
        try {
            const targetTab = await this.getTargetTab();
            if (!targetTab) {
                alert('Please open a web page (http:// or https://) in another tab for mouse tracking.');
                return;
            }

            this.currentTab = targetTab;
            this.debugInfo.style.display = 'block';
            this.debugText.textContent = 'Starting mouse tracking test...';

            await this.startMouseTracking();

            // Set a timeout to stop tracking and show results
            setTimeout(async () => {
                await this.stopMouseTracking();

                // Request mouse data from background
                chrome.runtime.sendMessage({
                    origin: 'control',
                    content: { action: 'stop' }
                });

                this.debugText.textContent = 'Test completed. Check console for results.';
            }, 10000); // Test for 10 seconds

        } catch (error) {
            console.error('Error testing mouse tracking:', error);
            this.debugText.textContent = `Test error: ${error.message}`;
        }
    }

    async openTestPage() {
        try {
            // Open a simple web page for testing
            // Using httpbin.org which provides a simple HTML page
            const testUrl = 'https://httpbin.org/html';
            await chrome.tabs.create({ url: testUrl });

            // Update target tab info after a short delay
            setTimeout(() => {
                this.updateTargetTabInfo();
            }, 1000);

            this.debugText.textContent = 'Test page opened. You can now start recording and mouse tracking.';

        } catch (error) {
            console.error('Error opening test page:', error);
            this.debugText.textContent = `Error opening test page: ${error.message}`;
        }
    }

    async startRecording() {
        try {
            const recordingType = document.querySelector('input[name="recordingType"]:checked').value;
            const enableMouseTracking = document.getElementById('enableMouseTracking').checked;

            // For tab recording and mouse tracking, we need a valid web page
            if (recordingType === 'tab' || enableMouseTracking) {
                // Get a suitable tab for recording/tracking
                const targetTab = await this.getTargetTab();
                if (!targetTab) {
                    const message = recordingType === 'tab'
                        ? 'Please open a web page (http:// or https://) in another tab for tab recording.'
                        : 'Please open a web page (http:// or https://) in another tab for mouse tracking.';
                    alert(message + '\n\nNote: The extension cannot record its own control page or Chrome internal pages.');
                    return;
                }
                this.currentTab = targetTab;

                // Update debug info
                this.debugText.textContent = `Using target tab: ${targetTab.title}`;
            }

            // Start screen recording
            await this.startScreenRecording(recordingType);

            // Start mouse tracking if enabled and we have a valid tab
            if (enableMouseTracking && this.currentTab) {
                await this.startMouseTracking();
            }

            this.isRecording = true;
            await chrome.storage.sync.set({ isRecording: true });
            this.updateUIState(true);

        } catch (error) {
            console.error('Error starting recording:', error);

            // Provide more helpful error messages
            let errorMessage = error.message;
            if (errorMessage.includes('activeTab permission')) {
                errorMessage = 'Cannot record this page. Please switch to a regular web page (http:// or https://) and try again.';
            } else if (errorMessage.includes('Chrome pages cannot be captured')) {
                errorMessage = 'Cannot record Chrome internal pages. Please open a regular web page and try again.';
            }

            alert(`Failed to start recording: ${errorMessage}`);
            this.debugText.textContent = `Error: ${errorMessage}`;
        }
    }

    async startScreenRecording(type) {
        try {
            let stream;

            if (type === 'tab') {
                // Make sure we have a valid tab and switch to it
                if (!this.currentTab) {
                    throw new Error('No suitable tab found for recording');
                }

                // Switch to the target tab first
                await chrome.tabs.update(this.currentTab.id, { active: true });
                await chrome.windows.update(this.currentTab.windowId, { focused: true });

                // Wait a moment for the tab to become active
                await new Promise(resolve => setTimeout(resolve, 500));

                // Record the tab using tabCapture
                stream = await new Promise((resolve, reject) => {
                    chrome.tabCapture.capture({
                        audio: true,
                        video: true
                    }, (capturedStream) => {
                        if (chrome.runtime.lastError) {
                            reject(new Error(chrome.runtime.lastError.message));
                        } else if (capturedStream) {
                            resolve(capturedStream);
                        } else {
                            reject(new Error('Failed to capture tab'));
                        }
                    });
                });
            } else {
                // Record window or screen using desktopCapture
                const sources = type === 'screen' ? ['screen'] : ['window'];
                const streamId = await new Promise((resolve, reject) => {
                    chrome.desktopCapture.chooseDesktopMedia(sources, (streamId) => {
                        if (streamId) {
                            resolve(streamId);
                        } else {
                            reject(new Error('User cancelled screen selection'));
                        }
                    });
                });

                // Get media stream using the streamId
                stream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        mandatory: {
                            chromeMediaSource: 'desktop',
                            chromeMediaSourceId: streamId
                        }
                    },
                    video: {
                        mandatory: {
                            chromeMediaSource: 'desktop',
                            chromeMediaSourceId: streamId
                        }
                    }
                });
            }

            // Setup MediaRecorder
            this.recordedChunks = [];

            // Try different codecs based on browser support
            let mimeType = 'video/webm;codecs=vp9';
            if (!MediaRecorder.isTypeSupported(mimeType)) {
                mimeType = 'video/webm;codecs=vp8';
                if (!MediaRecorder.isTypeSupported(mimeType)) {
                    mimeType = 'video/webm';
                }
            }

            this.mediaRecorder = new MediaRecorder(stream, { mimeType });

            this.mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    this.recordedChunks.push(event.data);
                }
            };

            this.mediaRecorder.onstop = () => {
                const blob = new Blob(this.recordedChunks, { type: 'video/webm' });
                const url = URL.createObjectURL(blob);
                this.recordedVideo.src = url;
                this.downloadBtn.disabled = false;

                // Stop all tracks to free up resources
                stream.getTracks().forEach(track => track.stop());
            };

            this.mediaRecorder.start();

        } catch (error) {
            console.error('Screen recording error:', error);
            throw error;
        }
    }

    async startMouseTracking() {
        try {
            // Clear previous mouse data and set session start time
            this.mouseData = [];
            this.sessionStartTime = Date.now();

            // Show debug info
            this.debugInfo.style.display = 'block';
            this.debugText.textContent = 'Injecting mouse tracker...';

            console.log('Starting mouse tracking for tab:', this.currentTab.id, this.currentTab.url);

            // Inject the content script file
            await chrome.scripting.executeScript({
                target: { tabId: this.currentTab.id },
                files: ['content-script.js']
            });

            console.log('Content script injected successfully');
            this.debugText.textContent = 'Content script injected, starting tracking...';

            // Wait a moment for the script to initialize
            await new Promise(resolve => setTimeout(resolve, 200));

            // Send message to start tracking
            const response = await chrome.tabs.sendMessage(this.currentTab.id, { action: 'startMouseTracking' });
            console.log('Start tracking response:', response);

            this.debugText.textContent = 'Mouse tracker active! Move your mouse on the target tab.';

            // Set badge to indicate recording
            await chrome.action.setBadgeText({ text: 'REC' });
            await chrome.action.setBadgeBackgroundColor({ color: '#F00' });

        } catch (error) {
            console.error('Error starting mouse tracking:', error);
            this.debugText.textContent = `Error: ${error.message}`;

            // Try to provide more helpful error messages
            if (error.message.includes('Cannot access contents of url')) {
                this.debugText.textContent = 'Error: Cannot access this page. Try a regular website (http/https).';
            } else if (error.message.includes('No tab with id')) {
                this.debugText.textContent = 'Error: Target tab not found. Please refresh and try again.';
            }

            throw error;
        }
    }



    async stopRecording() {
        try {
            // Stop screen recording
            if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
                this.mediaRecorder.stop();
            }

            // Stop mouse tracking
            await this.stopMouseTracking();

            this.isRecording = false;
            await chrome.storage.sync.set({ isRecording: false });
            this.updateUIState(false);

            // Request mouse data from background
            chrome.runtime.sendMessage({
                origin: 'control',
                content: { action: 'stop' }
            });

            // Check if auto-export is enabled
            if (this.autoExportToEditorCheckbox.checked) {
                // Show message about auto-export
                this.debugText.textContent = 'Recording stopped. Preparing to export to video editor...';

                // Wait a moment for everything to be processed, then auto-export
                setTimeout(async () => {
                    if (this.recordedChunks.length > 0 && this.mouseData.length > 0) {
                        await this.exportToVideoEditor();
                    } else {
                        this.debugText.textContent = 'Recording completed. Use "Export to Video Editor" when ready.';
                    }
                }, 2000);
            } else {
                this.debugText.textContent = 'Recording completed. Use "Export to Video Editor" when ready.';
            }

        } catch (error) {
            console.error('Error stopping recording:', error);
        }
    }

    async stopMouseTracking() {
        try {
            if (this.currentTab) {
                // Send message to stop tracking
                await chrome.tabs.sendMessage(this.currentTab.id, { action: 'stopMouseTracking' });
            }

            // Clear badge
            await chrome.action.setBadgeText({ text: '' });
            await chrome.action.setBadgeBackgroundColor({ color: '#FFF' });

        } catch (error) {
            console.error('Error stopping mouse tracking:', error);
            // Don't throw here as this is cleanup
        }
    }

    updateUIState(recording) {
        if (recording) {
            this.statusText.textContent = 'Recording...';
            this.statusDot.classList.add('recording');
            this.startBtn.disabled = true;
            this.stopBtn.disabled = false;
            this.downloadBtn.disabled = true;
            this.exportToVideoEditorBtn.disabled = true;
        } else {
            this.statusText.textContent = 'Ready';
            this.statusDot.classList.remove('recording');
            this.startBtn.disabled = false;
            this.stopBtn.disabled = true;
            this.resultsSection.style.display = 'block';
            // Enable export buttons only if we have both video and mouse data
            const hasRecording = this.recordedChunks.length > 0;
            const hasMouseData = this.mouseData.length > 0;
            this.downloadBtn.disabled = !hasRecording;
            this.exportToVideoEditorBtn.disabled = !(hasRecording && hasMouseData);
        }
    }

    handleMouseData(data) {
        console.log('Handling mouse data:', data.length, 'points');
        this.mouseData = data;
        this.debugText.textContent = `Received ${data.length} mouse points`;
        this.updateMouseStats();
        this.visualizeMouseData();
    }

    updateMouseStats() {
        const clicks = this.mouseData.filter(item => item.action === 'click').length;
        this.coordCount.textContent = this.mouseData.length;
        this.clickCount.textContent = clicks;
    }

    async visualizeMouseData() {
        if (this.mouseData.length === 0) return;

        // Create canvas for visualization
        const canvas = document.createElement('canvas');
        canvas.width = 400;
        canvas.height = 300;
        const ctx = canvas.getContext('2d');

        // Clear previous visualization
        this.mouseVisualization.innerHTML = '';

        // Get dimensions from the tracked tab if available
        let maxX = 1920, maxY = 1080; // Default dimensions

        try {
            if (this.currentTab) {
                const results = await chrome.scripting.executeScript({
                    target: { tabId: this.currentTab.id },
                    function: () => ({
                        width: window.innerWidth,
                        height: window.innerHeight
                    })
                });
                if (results && results[0] && results[0].result) {
                    maxX = results[0].result.width;
                    maxY = results[0].result.height;
                }
            }
        } catch (error) {
            console.warn('Could not get tab dimensions, using defaults');
        }

        // Draw mouse path
        ctx.strokeStyle = '#667eea';
        ctx.lineWidth = 2;
        ctx.beginPath();

        this.mouseData.forEach((item, index) => {
            const x = (item.coords.x / maxX) * canvas.width;
            const y = (item.coords.y / maxY) * canvas.height;

            if (index === 0) {
                ctx.moveTo(x, y);
            } else if (item.action === 'click') {
                // Draw click indicator
                ctx.fillStyle = '#e74c3c';
                ctx.fillRect(x - 3, y - 3, 6, 6);
            } else {
                ctx.lineTo(x, y);
                ctx.stroke();
            }
        });

        this.mouseVisualization.appendChild(canvas);
    }

    downloadRecording() {
        if (this.recordedChunks.length === 0) return;

        const blob = new Blob(this.recordedChunks, { type: 'video/webm' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `recording-${new Date().toISOString().slice(0, 19)}.webm`;
        a.click();
        URL.revokeObjectURL(url);
    }

    async exportToVideoEditor() {
        if (this.recordedChunks.length === 0 || this.mouseData.length === 0) {
            alert('No recording or mouse data available to export.');
            return;
        }

        try {
            // Create video blob
            const videoBlob = new Blob(this.recordedChunks, { type: 'video/webm' });
            const videoSizeMB = (videoBlob.size / 1024 / 1024).toFixed(2);

            this.debugText.textContent = `Preparing to export ${videoSizeMB}MB video with mouse tracking data...`;

            // Prepare mouse tracking data in standardized format
            const sessionId = `session-${this.sessionStartTime || Date.now()}`;
            const startTime = this.sessionStartTime || Date.now();
            const endTime = Date.now();
            const tabDimensions = this.currentTab ? await this.getTabDimensions() : { width: 1920, height: 1080 };

            const mouseTrackingData = {
                sessionId,
                startTime,
                endTime,
                tabInfo: {
                    title: this.currentTab ? this.currentTab.title : 'Unknown',
                    url: this.currentTab ? this.currentTab.url : 'Unknown',
                    dimensions: tabDimensions
                },
                events: this.mouseData.map(item => ({
                    timestamp: item.timestamp ? (item.timestamp - startTime) : 0, // Convert to relative timestamp
                    action: item.action,
                    coords: item.coords,
                    metadata: item.metadata || {}
                }))
            };

            // Create recording package
            const recordingPackage = {
                video: {
                    blob: videoBlob,
                    filename: `recording-${new Date().toISOString().slice(0, 19)}.webm`,
                    duration: this.recordedChunks.length > 0 ? await this.getVideoDuration(videoBlob) : 0,
                    dimensions: await this.getVideoDimensions(videoBlob),
                    mimeType: 'video/webm'
                },
                mouseTracking: mouseTrackingData,
                recordingMetadata: {
                    recordingType: document.querySelector('input[name="recordingType"]:checked').value,
                    timestamp: Date.now(),
                    sessionId: mouseTrackingData.sessionId
                }
            };

            // Store the recording package for the React app to pick up
            await this.sendToVideoEditor(recordingPackage);

            // Show success message
            this.debugText.textContent = 'Recording exported to video editor successfully!';

        } catch (error) {
            console.error('Error exporting to video editor:', error);

            // Provide specific error messages for common issues
            if (error.message.includes('quota') || error.message.includes('storage')) {
                this.debugText.textContent = `Export error: Video too large for browser storage (${(this.recordedChunks.reduce((size, chunk) => size + chunk.size, 0) / 1024 / 1024).toFixed(2)}MB). Using fallback method...`;
            } else {
                this.debugText.textContent = `Export error: ${error.message}`;
            }
        }
    }

    async getTabDimensions() {
        if (!this.currentTab) return { width: 1920, height: 1080 };

        try {
            const results = await chrome.scripting.executeScript({
                target: { tabId: this.currentTab.id },
                function: () => ({
                    width: window.innerWidth,
                    height: window.innerHeight
                })
            });
            return results && results[0] && results[0].result ? results[0].result : { width: 1920, height: 1080 };
        } catch (error) {
            console.warn('Could not get tab dimensions:', error);
            return { width: 1920, height: 1080 };
        }
    }

    async getVideoDuration(blob) {
        return new Promise((resolve) => {
            const video = document.createElement('video');
            video.onloadedmetadata = () => {
                resolve(video.duration * 1000); // Convert to milliseconds
                URL.revokeObjectURL(video.src);
            };
            video.onerror = () => {
                resolve(0);
                URL.revokeObjectURL(video.src);
            };
            video.src = URL.createObjectURL(blob);
        });
    }

    async getVideoDimensions(blob) {
        return new Promise((resolve) => {
            const video = document.createElement('video');
            video.onloadedmetadata = () => {
                resolve({
                    width: video.videoWidth,
                    height: video.videoHeight
                });
                URL.revokeObjectURL(video.src);
            };
            video.onerror = () => {
                resolve({ width: 1920, height: 1080 });
                URL.revokeObjectURL(video.src);
            };
            video.src = URL.createObjectURL(blob);
        });
    }

    async sendToVideoEditor(recordingPackage) {
        try {
            // Method 1: Use IndexedDB for large video files (more reliable than localStorage)
            await this.storeInIndexedDB(recordingPackage);

            // Method 2: Store metadata in localStorage as a pointer
            const storageKey = `video-editor-recording-${Date.now()}`;
            const metadataOnly = {
                sessionId: recordingPackage.recordingMetadata.sessionId,
                timestamp: Date.now(),
                processed: false,
                mouseTracking: recordingPackage.mouseTracking,
                recordingMetadata: recordingPackage.recordingMetadata,
                video: {
                    filename: recordingPackage.video.filename,
                    duration: recordingPackage.video.duration,
                    dimensions: recordingPackage.video.dimensions,
                    mimeType: recordingPackage.video.mimeType,
                    // Store in IndexedDB instead of localStorage
                    indexedDBKey: storageKey
                }
            };

            localStorage.setItem(storageKey, JSON.stringify(metadataOnly));
            localStorage.setItem('video-editor-latest-recording', storageKey);

            this.debugText.textContent = 'Recording data stored successfully!';

        } catch (error) {
            console.error('Error storing recording data:', error);

            // Fallback: Try to use a temporary download approach
            await this.fallbackToDownloadMethod(recordingPackage);
        }

        // Method 3: Try to open/focus the React video editor (if enabled)
        if (this.autoOpenEditorCheckbox.checked) {
            await this.openVideoEditor();
        } else {
            this.debugText.textContent = 'Recording exported successfully! Open the video editor manually to import it.';
        }
    }

    async storeInIndexedDB(recordingPackage) {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open('VideoEditorRecordings', 1);

            request.onerror = () => reject(request.error);

            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                if (!db.objectStoreNames.contains('recordings')) {
                    db.createObjectStore('recordings', { keyPath: 'id' });
                }
            };

            request.onsuccess = (event) => {
                const db = event.target.result;
                const transaction = db.transaction(['recordings'], 'readwrite');
                const store = transaction.objectStore('recordings');

                const recordingData = {
                    id: `video-editor-recording-${Date.now()}`,
                    videoBlob: recordingPackage.video.blob,
                    metadata: recordingPackage,
                    timestamp: Date.now()
                };

                const addRequest = store.add(recordingData);

                addRequest.onsuccess = () => {
                    console.log('Recording stored in IndexedDB successfully');
                    resolve(recordingData.id);
                };

                addRequest.onerror = () => reject(addRequest.error);
            };
        });
    }

    async fallbackToDownloadMethod(recordingPackage) {
        // Create a unique identifier for this recording
        const recordingId = `recording-${Date.now()}`;

        // Store metadata in localStorage
        const metadataKey = `video-editor-metadata-${recordingId}`;
        const metadata = {
            recordingId,
            mouseTracking: recordingPackage.mouseTracking,
            recordingMetadata: recordingPackage.recordingMetadata,
            video: {
                filename: recordingPackage.video.filename,
                duration: recordingPackage.video.duration,
                dimensions: recordingPackage.video.dimensions,
                mimeType: recordingPackage.video.mimeType
            },
            timestamp: Date.now(),
            processed: false,
            method: 'download-fallback'
        };

        localStorage.setItem(metadataKey, JSON.stringify(metadata));
        localStorage.setItem('video-editor-latest-recording', metadataKey);

        // Create a download link for the video file
        const blob = recordingPackage.video.blob;
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${recordingId}.webm`;
        a.style.display = 'none';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);

        // Clean up the URL after a delay
        setTimeout(() => URL.revokeObjectURL(url), 1000);

        this.debugText.textContent = 'Video downloaded. Import the file manually in the video editor along with the mouse tracking data.';
    }



    async openVideoEditor() {
        try {
            this.debugText.textContent = 'Opening video editor...';

            // Try to find an existing video editor tab
            const tabs = await chrome.tabs.query({});
            const videoEditorTab = tabs.find(tab =>
                tab.url && (
                    tab.url.includes('localhost:5173') ||
                    tab.url.includes('localhost:3000') ||
                    tab.url.includes('localhost:5174') || // Vite sometimes uses this port
                    tab.url.includes('video-editor') ||
                    (tab.title && tab.title.toLowerCase().includes('video editor'))
                )
            );

            if (videoEditorTab) {
                // Focus existing tab
                await chrome.tabs.update(videoEditorTab.id, { active: true });
                await chrome.windows.update(videoEditorTab.windowId, { focused: true });
                this.debugText.textContent = 'Video editor focused. Recording data will be imported automatically.';
            } else {
                // Try multiple common development URLs
                const possibleUrls = [
                    'http://localhost:5173',
                    'http://localhost:3000',
                    'http://localhost:5174',
                    'http://127.0.0.1:5173',
                    'http://127.0.0.1:3000'
                ];

                // Open new tab with video editor (try the most common port first)
                const newTab = await chrome.tabs.create({
                    url: possibleUrls[0],
                    active: true
                });

                // Check if the page loads successfully
                setTimeout(() => {
                    chrome.tabs.get(newTab.id, (tab) => {
                        if (chrome.runtime.lastError || !tab || tab.url === 'chrome-error://chromewebdata/') {
                            this.debugText.textContent = 'Could not open video editor. Please make sure it\'s running on localhost:5173';
                        } else {
                            this.debugText.textContent = 'Video editor opened. Recording data will be imported automatically.';
                        }
                    });
                }, 2000);
            }
        } catch (error) {
            console.error('Error opening video editor:', error);
            this.debugText.textContent = `Error opening video editor: ${error.message}`;
        }
    }

    copyMouseData() {
        const data = {
            mouseActions: this.mouseData,
            recordingInfo: {
                totalCoordinates: this.mouseData.length,
                totalClicks: this.mouseData.filter(item => item.action === 'click').length,
                timestamp: new Date().toISOString()
            }
        };

        navigator.clipboard.writeText(JSON.stringify(data, null, 2)).then(() => {
            // Show feedback
            const originalText = this.copyMouseDataBtn.textContent;
            this.copyMouseDataBtn.textContent = 'Copied!';
            setTimeout(() => {
                this.copyMouseDataBtn.textContent = originalText;
            }, 2000);
        });
    }
}

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
    new MouseTrackerRecorder();
});
