import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useExtensionBridge, useUnprocessedRecordings } from '../hooks/use-extension-bridge';
import { 
  RefreshCw, 
  Download, 
  Trash2, 
  CheckCircle, 
  AlertCircle, 
  Info,
  ChevronDown,
  ChevronUp,
  Monitor
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface ExtensionBridgeStatusProps {
  className?: string;
}

export function ExtensionBridgeStatus({ className }: ExtensionBridgeStatusProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const {
    isConnected,
    stats,
    error,
    checkForRecordings,
    cleanupOldRecordings,
    refreshStats,
  } = useExtensionBridge();

  const { recordings: unprocessedRecordings, refreshRecordings } = useUnprocessedRecordings();

  const handleRefresh = async () => {
    await checkForRecordings();
    refreshRecordings();
    refreshStats();
  };

  const handleCleanup = () => {
    cleanupOldRecordings();
    refreshRecordings();
  };

  const getStatusColor = () => {
    if (error) return 'destructive';
    if (stats.unprocessed > 0) return 'default';
    return 'secondary';
  };

  const getStatusIcon = () => {
    if (error) return <AlertCircle className="h-4 w-4" />;
    if (stats.unprocessed > 0) return <Info className="h-4 w-4" />;
    return <CheckCircle className="h-4 w-4" />;
  };

  const getStatusText = () => {
    if (error) return 'Error';
    if (stats.unprocessed > 0) return `${stats.unprocessed} pending`;
    return 'Connected';
  };

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Monitor className="h-5 w-5 text-muted-foreground" />
            <div>
              <CardTitle className="text-sm font-medium">Extension Bridge</CardTitle>
              <CardDescription className="text-xs">
                Browser extension integration
              </CardDescription>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant={getStatusColor()} className="text-xs">
              {getStatusIcon()}
              <span className="ml-1">{getStatusText()}</span>
            </Badge>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="h-8 w-8 p-0"
            >
              {isExpanded ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>
      </CardHeader>

      {isExpanded && (
        <CardContent className="pt-0">
          {/* Error Display */}
          {error && (
            <div className="mb-4 p-3 bg-destructive/10 border border-destructive/20 rounded-md">
              <div className="flex items-start gap-2">
                <AlertCircle className="h-4 w-4 text-destructive mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-sm font-medium text-destructive">Connection Error</p>
                  <p className="text-xs text-destructive/80">{error}</p>
                </div>
              </div>
            </div>
          )}

          {/* Stats */}
          <div className="grid grid-cols-3 gap-4 mb-4">
            <div className="text-center">
              <div className="text-lg font-semibold">{stats.total}</div>
              <div className="text-xs text-muted-foreground">Total</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-green-600">{stats.processed}</div>
              <div className="text-xs text-muted-foreground">Processed</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-orange-600">{stats.unprocessed}</div>
              <div className="text-xs text-muted-foreground">Pending</div>
            </div>
          </div>

          {/* Unprocessed Recordings */}
          {unprocessedRecordings.length > 0 && (
            <div className="mb-4">
              <h4 className="text-sm font-medium mb-2">Pending Recordings</h4>
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {unprocessedRecordings.map((recording, index) => (
                  <div
                    key={`${recording.recordingMetadata.sessionId}-${index}`}
                    className="flex items-center justify-between p-2 bg-muted/50 rounded-md"
                  >
                    <div className="flex-1 min-w-0">
                      <p className="text-xs font-medium truncate">
                        {recording.mouseTracking.tabInfo.title}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {new Date(recording.timestamp).toLocaleTimeString()}
                      </p>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {recording.recordingMetadata.recordingType}
                    </Badge>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              className="flex-1"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleCleanup}
              className="flex-1"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Cleanup
            </Button>
          </div>

          {/* Help Text */}
          <div className="mt-4 p-3 bg-muted/30 rounded-md">
            <p className="text-xs text-muted-foreground">
              <strong>How to use:</strong> Install the browser extension, record your screen with mouse tracking, 
              then click "Export to Video Editor" to automatically import recordings here.
            </p>
          </div>
        </CardContent>
      )}
    </Card>
  );
}

/**
 * Compact version for toolbar/header
 */
export function ExtensionBridgeStatusCompact({ className }: ExtensionBridgeStatusProps) {
  const { stats, error } = useExtensionBridge();

  const getStatusColor = () => {
    if (error) return 'destructive';
    if (stats.unprocessed > 0) return 'default';
    return 'secondary';
  };

  const getStatusIcon = () => {
    if (error) return <AlertCircle className="h-3 w-3" />;
    if (stats.unprocessed > 0) return <Info className="h-3 w-3" />;
    return <CheckCircle className="h-3 w-3" />;
  };

  const getStatusText = () => {
    if (error) return 'Extension Error';
    if (stats.unprocessed > 0) return `${stats.unprocessed} recordings pending`;
    return 'Extension connected';
  };

  return (
    <Badge variant={getStatusColor()} className={cn("text-xs", className)}>
      {getStatusIcon()}
      <span className="ml-1">{getStatusText()}</span>
    </Badge>
  );
}
