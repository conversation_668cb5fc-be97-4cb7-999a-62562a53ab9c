/**
 * Extension Bridge Service
 * Handles communication between the browser extension and React video editor
 */

export interface ExtensionRecordingData {
  video: {
    blobData: string; // Base64 encoded video data
    filename: string;
    duration: number;
    dimensions: { width: number; height: number; };
    mimeType: string;
  };
  mouseTracking: {
    sessionId: string;
    startTime: number;
    endTime: number;
    tabInfo: {
      title: string;
      url: string;
      dimensions: { width: number; height: number; };
    };
    events: Array<{
      timestamp: number;
      action: 'move' | 'click' | 'scroll';
      coords: { x: number; y: number; };
      metadata?: any;
    }>;
  };
  recordingMetadata: {
    recordingType: 'tab' | 'window' | 'screen';
    timestamp: number;
    sessionId: string;
  };
  timestamp: number;
  processed: boolean;
}

export class ExtensionBridge {
  private static instance: ExtensionBridge;
  private pollingInterval: number | null = null;
  private onNewRecordingCallback: ((data: ExtensionRecordingData) => void) | null = null;

  private constructor() {
    this.startPolling();
  }

  public static getInstance(): ExtensionBridge {
    if (!ExtensionBridge.instance) {
      ExtensionBridge.instance = new ExtensionBridge();
    }
    return ExtensionBridge.instance;
  }

  /**
   * Set callback for when new recordings are detected
   */
  public onNewRecording(callback: (data: ExtensionRecordingData) => void): void {
    this.onNewRecordingCallback = callback;
  }

  /**
   * Start polling for new recordings from the extension
   */
  private startPolling(): void {
    if (this.pollingInterval) return;

    this.pollingInterval = window.setInterval(() => {
      this.checkForNewRecordings();
    }, 2000); // Check every 2 seconds

    // Also check immediately
    this.checkForNewRecordings();
  }

  /**
   * Stop polling for new recordings
   */
  public stopPolling(): void {
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
      this.pollingInterval = null;
    }
  }

  /**
   * Check localStorage for new recordings from the extension
   */
  private async checkForNewRecordings(): Promise<void> {
    try {
      const latestRecordingKey = localStorage.getItem('video-editor-latest-recording');
      if (!latestRecordingKey) return;

      const recordingDataStr = localStorage.getItem(latestRecordingKey);
      if (!recordingDataStr) return;

      const recordingData: ExtensionRecordingData = JSON.parse(recordingDataStr);
      
      // Check if this recording has already been processed
      if (recordingData.processed) return;

      // Mark as processed to avoid duplicate imports
      recordingData.processed = true;
      localStorage.setItem(latestRecordingKey, JSON.stringify(recordingData));

      // Notify callback if set
      if (this.onNewRecordingCallback) {
        this.onNewRecordingCallback(recordingData);
      }

      console.log('📹 New recording detected from extension:', recordingData);

    } catch (error) {
      console.error('Error checking for new recordings:', error);
    }
  }

  /**
   * Convert base64 data back to Blob
   */
  public base64ToBlob(base64Data: string, mimeType: string): Blob {
    const byteCharacters = atob(base64Data.split(',')[1]); // Remove data:video/webm;base64, prefix
    const byteNumbers = new Array(byteCharacters.length);
    
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    
    const byteArray = new Uint8Array(byteNumbers);
    return new Blob([byteArray], { type: mimeType });
  }

  /**
   * Get all unprocessed recordings from localStorage
   */
  public getAllUnprocessedRecordings(): ExtensionRecordingData[] {
    const recordings: ExtensionRecordingData[] = [];
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('video-editor-recording-')) {
        try {
          const dataStr = localStorage.getItem(key);
          if (dataStr) {
            const data: ExtensionRecordingData = JSON.parse(dataStr);
            if (!data.processed) {
              recordings.push(data);
            }
          }
        } catch (error) {
          console.warn('Failed to parse recording data:', error);
        }
      }
    }
    
    return recordings.sort((a, b) => b.timestamp - a.timestamp); // Most recent first
  }

  /**
   * Clear old processed recordings from localStorage
   */
  public cleanupOldRecordings(maxAge: number = 24 * 60 * 60 * 1000): void {
    const now = Date.now();
    const keysToRemove: string[] = [];
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('video-editor-recording-')) {
        try {
          const dataStr = localStorage.getItem(key);
          if (dataStr) {
            const data: ExtensionRecordingData = JSON.parse(dataStr);
            if (data.processed && (now - data.timestamp) > maxAge) {
              keysToRemove.push(key);
            }
          }
        } catch (error) {
          // Remove corrupted entries
          keysToRemove.push(key);
        }
      }
    }
    
    keysToRemove.forEach(key => localStorage.removeItem(key));
    
    if (keysToRemove.length > 0) {
      console.log(`🧹 Cleaned up ${keysToRemove.length} old recordings`);
    }
  }

  /**
   * Manually trigger a check for new recordings
   */
  public async checkNow(): Promise<void> {
    await this.checkForNewRecordings();
  }

  /**
   * Get recording statistics
   */
  public getStats(): { total: number; processed: number; unprocessed: number } {
    let total = 0;
    let processed = 0;
    let unprocessed = 0;
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('video-editor-recording-')) {
        total++;
        try {
          const dataStr = localStorage.getItem(key);
          if (dataStr) {
            const data: ExtensionRecordingData = JSON.parse(dataStr);
            if (data.processed) {
              processed++;
            } else {
              unprocessed++;
            }
          }
        } catch (error) {
          // Count corrupted entries as unprocessed
          unprocessed++;
        }
      }
    }
    
    return { total, processed, unprocessed };
  }
}

// Export singleton instance
export const extensionBridge = ExtensionBridge.getInstance();
