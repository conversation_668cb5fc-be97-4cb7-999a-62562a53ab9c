import { create } from "zustand";
import { generateId } from "@designcombo/timeline";
import { getVideoMetadata } from "../utils/file";
import { useLocalVideosStore } from "./use-local-videos-store";
import { dispatch } from "@designcombo/events";
import { ADD_VIDEO } from "@designcombo/state";
import { IVideo } from "@designcombo/types";

// Mouse tracking data format for extension integration
export interface MouseTrackingData {
  sessionId: string;
  startTime: number;
  endTime: number;
  tabInfo: {
    title: string;
    url: string;
    dimensions: { width: number; height: number; };
  };
  events: Array<{
    timestamp: number;
    action: 'move' | 'click' | 'scroll';
    coords: { x: number; y: number; };
    metadata?: any;
  }>;
}

// Recording package from browser extension
export interface RecordingPackage {
  video: {
    blob: Blob;
    filename: string;
    duration: number;
    dimensions: { width: number; height: number; };
    mimeType: string;
  };
  mouseTracking: MouseTrackingData;
  recordingMetadata: {
    recordingType: 'tab' | 'window' | 'screen';
    timestamp: number;
    sessionId: string;
  };
}

export interface ExternalRecording {
  id: string;
  blob: Blob;
  name: string;
  duration: number;
  width: number;
  height: number;
  aspectRatio: number;
  objectUrl: string;
  thumbnailUrl?: string;
  type: "external-recording";
  recordedAt: Date;
  mimeType: string;
  mouseTracking?: MouseTrackingData;
  source: "browser-extension" | "manual-upload";
}

export interface ExternalRecordingState {
  recordings: ExternalRecording[];
  isProcessing: boolean;
  currentRecording: ExternalRecording | null;
  actions: {
    addExternalRecording: (recordingPackage: RecordingPackage) => Promise<ExternalRecording>;
    addManualRecording: (blob: Blob, duration: number, mimeType?: string) => Promise<ExternalRecording>;
    removeRecording: (id: string) => void;
    clearAll: () => void;
    getRecordingById: (id: string) => ExternalRecording | undefined;
    addToTimeline: (recording: ExternalRecording) => Promise<void>;
    setCurrentRecording: (recording: ExternalRecording | null) => void;
    importFromExtension: () => Promise<void>;
  };
}

// Helper function to create video thumbnail from blob
const createThumbnailFromBlob = (blob: Blob): Promise<string> => {
  return new Promise((resolve, reject) => {
    const video = document.createElement("video");
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");

    if (!ctx) {
      reject(new Error("Could not get canvas context"));
      return;
    }

    const objectUrl = URL.createObjectURL(blob);
    
    const timeout = setTimeout(() => {
      reject(new Error("Thumbnail generation timed out"));
      URL.revokeObjectURL(objectUrl);
    }, 10000);

    video.onloadedmetadata = () => {
      canvas.width = 160;
      canvas.height = 90;
      video.currentTime = Math.min(1, video.duration * 0.1);
    };

    video.onseeked = () => {
      try {
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
        const thumbnailUrl = canvas.toDataURL("image/jpeg", 0.8);
        clearTimeout(timeout);
        URL.revokeObjectURL(objectUrl);
        resolve(thumbnailUrl);
      } catch (error) {
        clearTimeout(timeout);
        URL.revokeObjectURL(objectUrl);
        reject(error);
      }
    };

    video.onerror = () => {
      clearTimeout(timeout);
      URL.revokeObjectURL(objectUrl);
      reject(new Error("Failed to load video for thumbnail"));
    };

    video.src = objectUrl;
    video.load();
  });
};

// Helper function to get video dimensions from blob
const getVideoDimensions = (blob: Blob): Promise<{ width: number; height: number; duration: number }> => {
  return new Promise((resolve, reject) => {
    const video = document.createElement("video");
    const objectUrl = URL.createObjectURL(blob);

    const timeout = setTimeout(() => {
      reject(new Error("Video metadata loading timed out"));
      URL.revokeObjectURL(objectUrl);
    }, 10000);

    video.onloadedmetadata = () => {
      const dimensions = {
        width: video.videoWidth,
        height: video.videoHeight,
        duration: video.duration * 1000, // Convert to milliseconds
      };
      clearTimeout(timeout);
      URL.revokeObjectURL(objectUrl);
      resolve(dimensions);
    };

    video.onerror = () => {
      clearTimeout(timeout);
      URL.revokeObjectURL(objectUrl);
      reject(new Error("Failed to load video metadata"));
    };

    video.src = objectUrl;
    video.load();
  });
};

export const useExternalRecordingStore = create<ExternalRecordingState>((set, get) => ({
  recordings: [],
  isProcessing: false,
  currentRecording: null,

  actions: {
    addExternalRecording: async (recordingPackage: RecordingPackage) => {
      set({ isProcessing: true });

      try {
        const id = generateId();
        const { video, mouseTracking, recordingMetadata } = recordingPackage;
        const objectUrl = URL.createObjectURL(video.blob);
        const recordedAt = new Date(recordingMetadata.timestamp);

        // Generate a name based on timestamp and source
        const timestamp = recordedAt.toLocaleString().replace(/[/:]/g, '-');
        const name = `Extension Recording ${timestamp}`;

        // Get video dimensions
        const { width, height, duration: actualDuration } = await getVideoDimensions(video.blob);
        const aspectRatio = width / height;

        // Debug logging for external recording
        console.log('📹 EXTERNAL RECORDING DEBUG:');
        console.log(`📐 Recorded dimensions: ${width}x${height}`);
        console.log(`📊 Aspect ratio: ${aspectRatio}`);
        console.log(`⏱️ Duration: ${actualDuration}ms`);
        console.log(`📁 Blob size: ${(video.blob.size / 1024 / 1024).toFixed(2)}MB`);
        console.log(`🎭 MIME type: ${video.mimeType}`);
        console.log(`🖱️ Mouse events: ${mouseTracking.events.length}`);
        console.log(`🌐 Source tab: ${mouseTracking.tabInfo.title}`);

        // Create thumbnail
        let thumbnailUrl: string | undefined;
        try {
          thumbnailUrl = await createThumbnailFromBlob(video.blob);
        } catch (error) {
          console.warn("Failed to create thumbnail:", error);
          // Continue without thumbnail
        }

        const recording: ExternalRecording = {
          id,
          blob: video.blob,
          name,
          duration: actualDuration || video.duration,
          width,
          height,
          aspectRatio,
          objectUrl,
          thumbnailUrl,
          type: "external-recording",
          recordedAt,
          mimeType: video.mimeType,
          mouseTracking,
          source: "browser-extension",
        };

        set(state => ({
          recordings: [...state.recordings, recording],
          currentRecording: recording,
          isProcessing: false,
        }));

        return recording;
      } catch (error) {
        set({ isProcessing: false });
        console.error("Failed to process external recording:", error);
        throw error;
      }
    },

    addManualRecording: async (blob: Blob, duration: number, mimeType = "video/webm") => {
      set({ isProcessing: true });

      try {
        const id = generateId();
        const objectUrl = URL.createObjectURL(blob);
        const recordedAt = new Date();

        // Generate a name based on timestamp
        const timestamp = recordedAt.toLocaleString().replace(/[/:]/g, '-');
        const name = `Manual Upload ${timestamp}`;

        // Get video dimensions
        const { width, height, duration: actualDuration } = await getVideoDimensions(blob);
        const aspectRatio = width / height;

        // Create thumbnail
        let thumbnailUrl: string | undefined;
        try {
          thumbnailUrl = await createThumbnailFromBlob(blob);
        } catch (error) {
          console.warn("Failed to create thumbnail:", error);
        }

        const recording: ExternalRecording = {
          id,
          blob,
          name,
          duration: actualDuration || duration,
          width,
          height,
          aspectRatio,
          objectUrl,
          thumbnailUrl,
          type: "external-recording",
          recordedAt,
          mimeType,
          source: "manual-upload",
        };

        set(state => ({
          recordings: [...state.recordings, recording],
          currentRecording: recording,
          isProcessing: false,
        }));

        return recording;
      } catch (error) {
        set({ isProcessing: false });
        console.error("Failed to process manual recording:", error);
        throw error;
      }
    },

    removeRecording: (id: string) => {
      const { recordings } = get();
      const recording = recordings.find(r => r.id === id);

      if (recording) {
        // Clean up object URL
        URL.revokeObjectURL(recording.objectUrl);

        set(state => ({
          recordings: state.recordings.filter(r => r.id !== id),
          currentRecording: state.currentRecording?.id === id ? null : state.currentRecording,
        }));
      }
    },

    clearAll: () => {
      const { recordings } = get();

      // Clean up all object URLs
      recordings.forEach(recording => {
        URL.revokeObjectURL(recording.objectUrl);
      });

      set({
        recordings: [],
        currentRecording: null,
      });
    },

    getRecordingById: (id: string) => {
      const { recordings } = get();
      return recordings.find(r => r.id === id);
    },

    addToTimeline: async (recording: ExternalRecording) => {
      try {
        // Convert external recording to a File object for compatibility with local videos store
        const file = new File([recording.blob], recording.name, {
          type: recording.mimeType,
          lastModified: recording.recordedAt.getTime(),
        });

        // Add to local videos store first
        const localVideosStore = useLocalVideosStore.getState();
        const localVideo = await localVideosStore.actions.addVideo(file);

        // Automatically add to timeline with mouse tracking metadata
        const videoData: Partial<IVideo> = {
          id: generateId(),
          details: {
            src: localVideo.objectUrl,
            width: localVideo.width,
            height: localVideo.height,
            blur: 0,
            brightness: 100,
            flipX: false,
            flipY: false,
            rotate: "0",
            visibility: "visible",
          },
          type: "video",
          metadata: {
            previewUrl: localVideo.thumbnailUrl || localVideo.objectUrl,
            localVideoId: localVideo.id,
            fileName: localVideo.name,
            mouseTracking: recording.mouseTracking, // Include mouse tracking data
            source: recording.source,
            externalRecordingId: recording.id,
          },
          duration: localVideo.duration,
        };

        dispatch(ADD_VIDEO, {
          payload: videoData,
          options: {
            resourceId: "main",
            scaleMode: "fit",
          },
        });

        console.log("External recording added to timeline successfully");
      } catch (error) {
        console.error("Failed to add external recording to timeline:", error);
        throw error;
      }
    },

    setCurrentRecording: (recording: ExternalRecording | null) => {
      set({ currentRecording: recording });
    },

    importFromExtension: async () => {
      // This will be implemented to check for new recordings from the browser extension
      // For now, it's a placeholder for the communication bridge
      console.log("Checking for new recordings from browser extension...");

      // TODO: Implement communication bridge with browser extension
      // This could involve:
      // 1. Checking localStorage for new recording data
      // 2. Listening for postMessage events
      // 3. Polling a shared file location
      // 4. Using native messaging
    },
  },
}));

// Helper hook to get just the actions
export const useExternalRecordingActions = () => useExternalRecordingStore(state => state.actions);

// Helper hook to get recordings
export const useExternalRecordings = () => useExternalRecordingStore(state => state.recordings);

// Helper hook to get current recording
export const useCurrentExternalRecording = () => useExternalRecordingStore(state => state.currentRecording);

// Helper hook to get processing state
export const useExternalRecordingProcessing = () => useExternalRecordingStore(state => state.isProcessing);

// Legacy exports for backward compatibility (will be removed after full migration)
export const useScreenRecordingActions = useExternalRecordingActions;
export const useScreenRecordings = useExternalRecordings;
export const useCurrentScreenRecording = useCurrentExternalRecording;
export const useScreenRecordingProcessing = useExternalRecordingProcessing;
